using Microsoft.EntityFrameworkCore;
using Models.HandleData;
using Models.Models;
using System.Text.RegularExpressions;

namespace WinFormsApp1
{
    public partial class frmThemDonVi : Form
    {
        private int deTaiId;
        private int? donViId; // null = thêm mới, có giá trị = sửa

        public frmThemDonVi(int deTaiId, int? donViId = null)
        {
            this.deTaiId = deTaiId;
            this.donViId = donViId;
            InitializeComponent();

            // Set form title after InitializeComponent
            this.Text = donViId.HasValue ? "Sửa đơn vị phối hợp" : "Thêm đơn vị phối hợp";

            SetupValidationEvents();

            if (donViId.HasValue)
            {
                LoadDonViData();
            }
        }

        private void SetupValidationEvents()
        {
            // Real-time validation khi người dùng gõ
            txtTenDonVi.TextChanged += (s, e) => ValidateField(txtTenDonVi, lblErrorTenDonVi, "Vui lòng nhập tên đơn vị!");
            txtDienThoai.TextChanged += (s, e) => ValidatePhoneField(txtDienThoai, lblErrorDienThoai);
            txtEmail.TextChanged += (s, e) => ValidateEmailField(txtEmail, lblErrorEmail);
        }

        private void ValidateField(TextBox textBox, Label errorLabel, string errorMessage)
        {
            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                ShowError(errorLabel, errorMessage);
            }
            else
            {
                HideError(errorLabel);
            }
        }

        private void ValidatePhoneField(TextBox textBox, Label errorLabel)
        {
            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                HideError(errorLabel); // Không bắt buộc nên ẩn lỗi khi trống
                return;
            }

            string phone = textBox.Text.Trim();
            if (Regex.IsMatch(phone, @"^[0-9]{8,15}$"))
            {
                HideError(errorLabel);
            }
            else
            {
                ShowError(errorLabel, "Số điện thoại chỉ được chứa số (8-15 ký tự)!");
            }
        }

        private void ValidateEmailField(TextBox textBox, Label errorLabel)
        {
            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                HideError(errorLabel); // Không bắt buộc nên ẩn lỗi khi trống
                return;
            }

            string email = textBox.Text.Trim();
            string emailPattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
            if (Regex.IsMatch(email, emailPattern))
            {
                HideError(errorLabel);
            }
            else
            {
                ShowError(errorLabel, "Email không đúng định dạng!");
            }
        }

        private void ClearAllErrors()
        {
            lblErrorTenDonVi.Visible = false;
            lblErrorDienThoai.Visible = false;
            lblErrorEmail.Visible = false;
        }

        private void ShowError(Label errorLabel, string message)
        {
            errorLabel.Text = message;
            errorLabel.Visible = true;
        }

        private void HideError(Label errorLabel)
        {
            errorLabel.Visible = false;
        }

        private bool ValidateForm()
        {
            ClearAllErrors();
            bool isValid = true;
            Control? firstErrorControl = null;

            // Validate Tên đơn vị (bắt buộc)
            if (string.IsNullOrWhiteSpace(txtTenDonVi.Text))
            {
                ShowError(lblErrorTenDonVi, "Vui lòng nhập tên đơn vị!");
                isValid = false;
                firstErrorControl ??= txtTenDonVi;
            }

            // Validate Số điện thoại (nếu có nhập)
            if (!string.IsNullOrWhiteSpace(txtDienThoai.Text))
            {
                string phone = txtDienThoai.Text.Trim();
                // Kiểm tra chỉ chứa số và độ dài hợp lý (8-15 ký tự)
                if (!Regex.IsMatch(phone, @"^[0-9]{8,15}$"))
                {
                    ShowError(lblErrorDienThoai, "Số điện thoại chỉ được chứa số (8-15 ký tự)!");
                    isValid = false;
                    firstErrorControl ??= txtDienThoai;
                }
            }

            // Validate Email (nếu có nhập)
            if (!string.IsNullOrWhiteSpace(txtEmail.Text))
            {
                string email = txtEmail.Text.Trim();
                // Regex pattern cho email
                string emailPattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";
                if (!Regex.IsMatch(email, emailPattern))
                {
                    ShowError(lblErrorEmail, "Email không đúng định dạng!");
                    isValid = false;
                    firstErrorControl ??= txtEmail;
                }
            }

            // Focus vào control đầu tiên có lỗi
            firstErrorControl?.Focus();

            return isValid;
        }

        private async void LoadDonViData()
        {
            if (!donViId.HasValue) return;

            try
            {
                using (var context = new DAContext())
                {
                    var donVi = await context.DonViPhoiHop.FirstOrDefaultAsync(dv => dv.MaDonVi == donViId.Value);
                    if (donVi != null)
                    {
                        txtTenDonVi.Text = donVi.TenDonVi;
                        txtDiaChi.Text = donVi.DiaChi;
                        txtDienThoai.Text = donVi.SoDienThoai;
                        txtEmail.Text = donVi.Email;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi tải dữ liệu: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnLuu_Click(object sender, EventArgs e)
        {
            // Validate form trước khi lưu
            if (!ValidateForm())
            {
                return;
            }

            try
            {
                using (var context = new DAContext())
                {
                    DonViPhoiHop donVi;

                    if (donViId.HasValue)
                    {
                        // Sửa đơn vị
                        donVi = await context.DonViPhoiHop.FirstOrDefaultAsync(dv => dv.MaDonVi == donViId.Value);
                        if (donVi == null)
                        {
                            MessageBox.Show("Không tìm thấy đơn vị!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }
                    }
                    else
                    {
                        // Thêm mới đơn vị
                        donVi = new DonViPhoiHop();
                        context.DonViPhoiHop.Add(donVi);
                    }

                    donVi.TenDonVi = txtTenDonVi.Text.Trim();
                    donVi.DiaChi = txtDiaChi.Text.Trim();
                    donVi.SoDienThoai = txtDienThoai.Text.Trim();
                    donVi.Email = txtEmail.Text.Trim();

                    await context.SaveChangesAsync();

                    // Nếu là thêm mới, cần liên kết với đề tài
                    if (!donViId.HasValue)
                    {
                        var deTaiDonVi = new DeTai_DonVi
                        {
                            MaDeTai = deTaiId,
                            MaDonVi = donVi.MaDonVi
                        };
                        context.DeTai_DonVi.Add(deTaiDonVi);
                        await context.SaveChangesAsync();
                    }

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi lưu: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnHuy_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
