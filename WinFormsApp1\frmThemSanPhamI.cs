using Microsoft.EntityFrameworkCore;
using Models.HandleData;
using Models.Models;

namespace WinFormsApp1
{
    public partial class frmThemSanPhamI : Form
    {
        private int deTaiId;
        private int? sanPhamId; // null = thêm mới, có giá trị = sửa
        private byte[]? fileData;

        public frmThemSanPhamI(int deTaiId, int? sanPhamId = null)
        {
            this.deTaiId = deTaiId;
            this.sanPhamId = sanPhamId;
            InitializeComponent();

            // Set form title after InitializeComponent
            this.Text = sanPhamId.HasValue ? "Sửa sản phẩm dạng I" : "Thêm sản phẩm dạng I";

            LoadDonViHanhChinh();
            SetupValidationEvents();

            if (sanPhamId.HasValue)
            {
                LoadSanPhamData();
            }
        }

        private void SetupValidationEvents()
        {
            // Real-time validation khi người dùng gõ
            txtTenSanPham.TextChanged += (s, e) => ValidateField(txtTenSanPham, lblErrorTenSanPham, "Vui lòng nhập tên sản phẩm!");
        }

        private void ValidateField(TextBox textBox, Label errorLabel, string errorMessage)
        {
            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                ShowError(errorLabel, errorMessage);
            }
            else
            {
                HideError(errorLabel);
            }
        }





        private async void LoadDonViHanhChinh()
        {
            try
            {
                using (var context = new DAContext())
                {
                    var donViList = await context.DonViHanhChinh.ToListAsync();
                    cmbDonViHanhChinh.Items.Clear();
                    cmbDonViHanhChinh.Items.Add(new ComboBoxItem { Text = "-- Chọn đơn vị --", Value = 0 });

                    foreach (var donVi in donViList)
                    {
                        cmbDonViHanhChinh.Items.Add(new ComboBoxItem { Text = donVi.TinhThanh, Value = donVi.MaDonViHC });
                    }

                    cmbDonViHanhChinh.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi tải đơn vị hành chính: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void LoadSanPhamData()
        {
            if (!sanPhamId.HasValue) return;

            try
            {
                using (var context = new DAContext())
                {
                    var sanPham = await context.ChiTietSanPham_DangI.FirstOrDefaultAsync(sp => sp.MaSanPham_I == sanPhamId.Value);
                    if (sanPham != null)
                    {
                        txtTenSanPham.Text = sanPham.TenSanPham_I;

                        // Set đơn vị hành chính
                        for (int i = 0; i < cmbDonViHanhChinh.Items.Count; i++)
                        {
                            var item = (ComboBoxItem)cmbDonViHanhChinh.Items[i]!;
                            if (item.Value == sanPham.MaDonViHC)
                            {
                                cmbDonViHanhChinh.SelectedIndex = i;
                                break;
                            }
                        }

                        // Load file data if exists
                        if (sanPham.file_SanPham_I != null)
                        {
                            fileData = sanPham.file_SanPham_I;
                            lblFile.Text = $"File đã có ({fileData.Length / 1024} KB)";
                            lblFile.ForeColor = Color.Green;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi tải dữ liệu: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnChonFile_Click(object sender, EventArgs e)
        {
            using (var openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "All files (*.*)|*.*|PDF files (*.pdf)|*.pdf|Word files (*.doc;*.docx)|*.doc;*.docx|Excel files (*.xls;*.xlsx)|*.xls;*.xlsx";
                openFileDialog.FilterIndex = 1;

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        fileData = File.ReadAllBytes(openFileDialog.FileName);
                        lblFile.Text = $"Đã chọn: {Path.GetFileName(openFileDialog.FileName)} ({fileData.Length / 1024} KB)";
                        lblFile.ForeColor = Color.Green;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Lỗi khi đọc file: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void ClearAllErrors()
        {
            lblErrorTenSanPham.Visible = false;
        }

        private void ShowError(Label errorLabel, string message)
        {
            errorLabel.Text = message;
            errorLabel.Visible = true;
        }

        private void HideError(Label errorLabel)
        {
            errorLabel.Visible = false;
        }

        private bool ValidateForm()
        {
            ClearAllErrors();
            bool isValid = true;
            Control? firstErrorControl = null;

            // Validate Tên sản phẩm (bắt buộc)
            if (string.IsNullOrWhiteSpace(txtTenSanPham.Text))
            {
                ShowError(lblErrorTenSanPham, "Vui lòng nhập tên sản phẩm!");
                isValid = false;
                firstErrorControl ??= txtTenSanPham;
            }

            // Focus vào control đầu tiên có lỗi
            firstErrorControl?.Focus();

            return isValid;
        }

        private async void BtnLuu_Click(object sender, EventArgs e)
        {
            if (!ValidateForm())
            {
                return;
            }

            try
            {
                using (var context = new DAContext())
                {
                    ChiTietSanPham_DangI sanPham;

                    if (sanPhamId.HasValue)
                    {
                        // Sửa sản phẩm
                        sanPham = await context.ChiTietSanPham_DangI.FirstOrDefaultAsync(sp => sp.MaSanPham_I == sanPhamId.Value);
                        if (sanPham == null)
                        {
                            MessageBox.Show("Không tìm thấy sản phẩm!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }
                    }
                    else
                    {
                        // Thêm mới sản phẩm
                        sanPham = new ChiTietSanPham_DangI { MaDeTai = deTaiId };
                        context.ChiTietSanPham_DangI.Add(sanPham);
                    }

                    // Set các thuộc tính theo model mới
                    sanPham.TenSanPham_I = txtTenSanPham.Text.Trim();

                    // Set đơn vị hành chính (bắt buộc)
                    if (cmbDonViHanhChinh.SelectedItem is ComboBoxItem selectedItem && selectedItem.Value > 0)
                    {
                        sanPham.MaDonViHC = selectedItem.Value;
                    }
                    else
                    {
                        MessageBox.Show("Vui lòng chọn đơn vị hành chính!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // Set file data
                    if (fileData != null)
                    {
                        sanPham.file_SanPham_I = fileData;
                    }

                    await context.SaveChangesAsync();

                    DialogResult = DialogResult.OK;
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi lưu: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnHuy_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
