using Microsoft.EntityFrameworkCore;
using Models.HandleData;
using Models.Models;

namespace WinFormsApp1
{
    public partial class frmThemDacTinh : Form
    {
        private int sanPhamId;
        private int? dacTinhId; // null = thêm mới, có giá trị = sửa

        public frmThemDacTinh(int sanPhamId, int? dacTinhId = null)
        {
            this.sanPhamId = sanPhamId;
            this.dacTinhId = dacTinhId;
            InitializeComponent();

            // Set form title after InitializeComponent
            this.Text = dacTinhId.HasValue ? "Sửa đặc tính kỹ thuật" : "Thêm đặc tính kỹ thuật";

            SetupValidationEvents();

            if (dacTinhId.HasValue)
            {
                LoadDacTinhData();
            }
        }

        private void SetupValidationEvents()
        {
            // Real-time validation khi người dùng gõ
            txtTenDacTinh.TextChanged += (s, e) => ValidateField(txtTenDacTinh, lblErrorTenDacTinh, "Vui lòng nhập thông số!");
        }

        private void ValidateField(TextBox textBox, Label errorLabel, string errorMessage)
        {
            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                ShowError(errorLabel, errorMessage);
            }
            else
            {
                HideError(errorLabel);
            }
        }



        private async void LoadDacTinhData()
        {
            if (!dacTinhId.HasValue) return;

            try
            {
                using (var context = new DAContext())
                {
                    var dacTinh = await context.DacTinhKyThuat.FirstOrDefaultAsync(dt => dt.MaDacTinhKyThuat == dacTinhId.Value);
                    if (dacTinh != null)
                    {
                        txtTenDacTinh.Text = dacTinh.ThongSo ?? string.Empty;
                        txtGiaTri.Text = dacTinh.GiaTri?.ToString() ?? string.Empty;
                        txtDonVi.Text = dacTinh.DonViDo ?? string.Empty;
                        txtMoTa.Text = dacTinh.ChiChu ?? string.Empty;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi tải dữ liệu: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearAllErrors()
        {
            lblErrorTenDacTinh.Visible = false;
        }

        private void ShowError(Label errorLabel, string message)
        {
            errorLabel.Text = message;
            errorLabel.Visible = true;
        }

        private void HideError(Label errorLabel)
        {
            errorLabel.Visible = false;
        }

        private bool ValidateForm()
        {
            ClearAllErrors();
            bool isValid = true;
            Control? firstErrorControl = null;

            // Validate Thông số (không bắt buộc nhưng nếu có thì phải hợp lệ)
            // Không cần validate gì đặc biệt vì tất cả đều nullable

            return isValid;
        }

        private async void BtnLuu_Click(object sender, EventArgs e)
        {
            if (!ValidateForm())
            {
                return;
            }



            try
            {
                using (var context = new DAContext())
                {
                    DacTinhKyThuat dacTinh;

                    if (dacTinhId.HasValue)
                    {
                        // Sửa đặc tính
                        dacTinh = await context.DacTinhKyThuat.FirstOrDefaultAsync(dt => dt.MaDacTinhKyThuat == dacTinhId.Value);
                        if (dacTinh == null)
                        {
                            MessageBox.Show("Không tìm thấy đặc tính!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }
                    }
                    else
                    {
                        // Thêm mới đặc tính
                        dacTinh = new DacTinhKyThuat { MaSanPham_I = sanPhamId };
                        context.DacTinhKyThuat.Add(dacTinh);
                    }

                    // Set các thuộc tính theo model mới
                    dacTinh.ThongSo = string.IsNullOrWhiteSpace(txtTenDacTinh.Text) ? null : txtTenDacTinh.Text.Trim();
                    dacTinh.DonViDo = string.IsNullOrWhiteSpace(txtDonVi.Text) ? null : txtDonVi.Text.Trim();
                    dacTinh.ChiChu = string.IsNullOrWhiteSpace(txtMoTa.Text) ? null : txtMoTa.Text.Trim();

                    // Parse giá trị số
                    if (decimal.TryParse(txtGiaTri.Text.Trim(), out decimal giaTri))
                    {
                        dacTinh.GiaTri = giaTri;
                    }
                    else
                    {
                        dacTinh.GiaTri = null;
                    }

                    await context.SaveChangesAsync();

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi lưu: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnHuy_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
