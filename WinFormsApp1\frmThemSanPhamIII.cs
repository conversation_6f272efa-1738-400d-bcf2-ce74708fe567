using Microsoft.EntityFrameworkCore;
using Models.HandleData;
using Models.Models;

namespace WinFormsApp1
{
    public partial class frmThemSanPhamIII : Form
    {
        private int deTaiId;
        private int? sanPhamId; // null = thêm mới, có giá trị = sửa
        private byte[]? fileData;

        public frmThemSanPhamIII(int deTaiId, int? sanPhamId = null)
        {
            this.deTaiId = deTaiId;
            this.sanPhamId = sanPhamId;
            InitializeComponent();

            // Set form title after InitializeComponent
            this.Text = sanPhamId.HasValue ? "Sửa sản phẩm dạng III" : "Thêm sản phẩm dạng III";

            LoadLoaiBaiBao();
            SetupValidationEvents();

            if (sanPhamId.HasValue)
            {
                LoadSanPhamData();
            }
        }

        private void LoadLoaiBaiBao()
        {
            cmbLoaiBaiBao.Items.Clear();
            cmbLoaiBaiBao.Items.AddRange(new string[] {
                "BangSangChe",
                "GiaiPhapHuuIch",
                "<PERSON>Bao"
            });
        }

        private void SetupValidationEvents()
        {
            // Real-time validation khi người dùng gõ
            txtTieuDe.TextChanged += (s, e) => ValidateField(txtTieuDe, lblErrorTieuDe, "Vui lòng nhập tên sản phẩm!");
            cmbLoaiBaiBao.SelectedIndexChanged += (s, e) => ValidateComboBox(cmbLoaiBaiBao, lblErrorLoaiBaiBao, "Vui lòng chọn loại sản phẩm!");
        }

        private void ValidateField(TextBox textBox, Label errorLabel, string errorMessage)
        {
            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                ShowError(errorLabel, errorMessage);
            }
            else
            {
                HideError(errorLabel);
            }
        }

        private void ValidateComboBox(ComboBox comboBox, Label errorLabel, string errorMessage)
        {
            if (comboBox.SelectedIndex < 0)
            {
                ShowError(errorLabel, errorMessage);
            }
            else
            {
                HideError(errorLabel);
            }
        }





        private async void LoadSanPhamData()
        {
            if (!sanPhamId.HasValue) return;

            try
            {
                using (var context = new DAContext())
                {
                    var sanPham = await context.ChiTietSanPham_DangIII.FirstOrDefaultAsync(sp => sp.MaSanPham_III == sanPhamId.Value);
                    if (sanPham != null)
                    {
                        txtTieuDe.Text = sanPham.TenSanPham_III;
                        txtTacGia.Text = sanPham.NoiCongBo ?? string.Empty;

                        // Set loại sản phẩm
                        string loaiSanPham = sanPham.LoaiSanPham_III.ToString();
                        int index = cmbLoaiBaiBao.Items.IndexOf(loaiSanPham);
                        if (index >= 0)
                            cmbLoaiBaiBao.SelectedIndex = index;

                        // Check file
                        if (sanPham.file_SanPham_III != null && sanPham.file_SanPham_III.Length > 0)
                        {
                            fileData = sanPham.file_SanPham_III;
                            lblFile.Text = $"Có file đính kèm ({fileData.Length / 1024} KB)";
                            lblFile.ForeColor = Color.Green;
                            btnTaiFile.Enabled = true;
                        }
                        else
                        {
                            btnTaiFile.Enabled = false;
                            lblFile.Text = "Chưa có file đính kèm";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi tải dữ liệu: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnChonFile_Click(object sender, EventArgs e)
        {
            using (var openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "All files (*.*)|*.*|PDF files (*.pdf)|*.pdf|Word files (*.doc;*.docx)|*.doc;*.docx|Excel files (*.xls;*.xlsx)|*.xls;*.xlsx";
                openFileDialog.FilterIndex = 1;

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        fileData = File.ReadAllBytes(openFileDialog.FileName);
                        lblFile.Text = $"Đã chọn: {Path.GetFileName(openFileDialog.FileName)} ({fileData.Length / 1024} KB)";
                        lblFile.ForeColor = Color.Green;
                        btnTaiFile.Enabled = true;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Lỗi khi đọc file: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void BtnTaiFile_Click(object sender, EventArgs e)
        {
            if (fileData == null || fileData.Length == 0)
            {
                MessageBox.Show("Không có file để tải!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            using (var saveFileDialog = new SaveFileDialog())
            {
                saveFileDialog.Filter = "All files (*.*)|*.*";
                saveFileDialog.FileName = $"SanPhamIII_{sanPhamId}";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        File.WriteAllBytes(saveFileDialog.FileName, fileData);
                        MessageBox.Show("Tải file thành công!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Lỗi khi tải file: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void ClearAllErrors()
        {
            lblErrorTieuDe.Visible = false;
            lblErrorLoaiBaiBao.Visible = false;
        }

        private void ShowError(Label errorLabel, string message)
        {
            errorLabel.Text = message;
            errorLabel.Visible = true;
        }

        private void HideError(Label errorLabel)
        {
            errorLabel.Visible = false;
        }

        private bool ValidateForm()
        {
            ClearAllErrors();
            bool isValid = true;
            Control? firstErrorControl = null;

            // Validate Tiêu đề (bắt buộc)
            if (string.IsNullOrWhiteSpace(txtTieuDe.Text))
            {
                ShowError(lblErrorTieuDe, "Vui lòng nhập tên sản phẩm!");
                isValid = false;
                firstErrorControl ??= txtTieuDe;
            }

            // Validate Loại sản phẩm (bắt buộc)
            if (cmbLoaiBaiBao.SelectedIndex < 0)
            {
                ShowError(lblErrorLoaiBaiBao, "Vui lòng chọn loại sản phẩm!");
                isValid = false;
                firstErrorControl ??= cmbLoaiBaiBao;
            }

            // Focus vào control đầu tiên có lỗi
            firstErrorControl?.Focus();

            return isValid;
        }

        private async void BtnLuu_Click(object sender, EventArgs e)
        {
            if (!ValidateForm())
            {
                return;
            }

            try
            {
                using (var context = new DAContext())
                {
                    ChiTietSanPham_DangIII sanPham;

                    if (sanPhamId.HasValue)
                    {
                        // Sửa sản phẩm
                        sanPham = await context.ChiTietSanPham_DangIII.FirstOrDefaultAsync(sp => sp.MaSanPham_III == sanPhamId.Value);
                        if (sanPham == null)
                        {
                            MessageBox.Show("Không tìm thấy sản phẩm!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }
                    }
                    else
                    {
                        // Thêm mới sản phẩm
                        sanPham = new ChiTietSanPham_DangIII { MaDeTai = deTaiId };
                        context.ChiTietSanPham_DangIII.Add(sanPham);
                    }

                    // Set các thuộc tính theo model mới
                    sanPham.TenSanPham_III = txtTieuDe.Text.Trim();
                    sanPham.NoiCongBo = txtTacGia.Text.Trim();

                    // Parse enum từ combobox
                    if (cmbLoaiBaiBao.SelectedItem != null)
                    {
                        string selectedValue = cmbLoaiBaiBao.SelectedItem.ToString()!;
                        if (Enum.TryParse<LoaiSanPham_III>(selectedValue, out var loaiSanPham))
                        {
                            sanPham.LoaiSanPham_III = loaiSanPham;
                        }
                    }

                    // Save file if selected
                    if (fileData != null && fileData.Length > 0)
                    {
                        sanPham.file_SanPham_III = fileData;
                    }

                    await context.SaveChangesAsync();

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi lưu: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnHuy_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
