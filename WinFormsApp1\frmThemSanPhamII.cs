using Microsoft.EntityFrameworkCore;
using Models.HandleData;
using Models.Models;

namespace WinFormsApp1
{
    public partial class frmThemSanPhamII : Form
    {
        private int deTaiId;
        private int? sanPhamId; // null = thêm mới, có giá trị = sửa
        private byte[]? fileData;

        public frmThemSanPhamII(int deTaiId, int? sanPhamId = null)
        {
            this.deTaiId = deTaiId;
            this.sanPhamId = sanPhamId;
            InitializeComponent();

            // Set form title after InitializeComponent
            this.Text = sanPhamId.HasValue ? "Sửa sản phẩm dạng II" : "Thêm sản phẩm dạng II";

            LoadLoaiBaoCao();
            SetupValidationEvents();

            if (sanPhamId.HasValue)
            {
                LoadSanPhamData();
            }
        }

        private void SetupValidationEvents()
        {
            // Real-time validation khi người dùng gõ
            txtTenBaoCao.TextChanged += (s, e) => ValidateField(txtTenBaoCao, lblErrorTenBaoCao, "Vui lòng nhập tên sản phẩm!");
            cmbLoaiBaoCao.SelectedIndexChanged += (s, e) => ValidateComboBox(cmbLoaiBaoCao, lblErrorLoaiBaoCao, "Vui lòng chọn loại sản phẩm!");
        }

        private void ValidateField(TextBox textBox, Label errorLabel, string errorMessage)
        {
            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                ShowError(errorLabel, errorMessage);
            }
            else
            {
                HideError(errorLabel);
            }
        }

        private void ValidateComboBox(ComboBox comboBox, Label errorLabel, string errorMessage)
        {
            if (comboBox.SelectedIndex < 0)
            {
                ShowError(errorLabel, errorMessage);
            }
            else
            {
                HideError(errorLabel);
            }
        }

        private void LoadLoaiBaoCao()
        {
            cmbLoaiBaoCao.Items.Clear();
            cmbLoaiBaoCao.Items.AddRange(new string[] {
                "BaoCao",
                "QuyTrinh",
                "BanVe",
                "BanDo",
                "Khac"
            });
        }

        private async void LoadSanPhamData()
        {
            if (!sanPhamId.HasValue) return;

            try
            {
                using (var context = new DAContext())
                {
                    var sanPham = await context.ChiTietSanPham_DangII.FirstOrDefaultAsync(sp => sp.MaSanPham_II == sanPhamId.Value);
                    if (sanPham != null)
                    {
                        txtTenBaoCao.Text = sanPham.TenSanPham_II;

                        // Set loại sản phẩm
                        string loaiSanPham = sanPham.LoaiSanPham_II.ToString();
                        int index = cmbLoaiBaoCao.Items.IndexOf(loaiSanPham);
                        if (index >= 0)
                            cmbLoaiBaoCao.SelectedIndex = index;

                        // Check file
                        if (sanPham.file_SanPham_II != null && sanPham.file_SanPham_II.Length > 0)
                        {
                            fileData = sanPham.file_SanPham_II;
                            lblFile.Text = $"Có file đính kèm ({fileData.Length / 1024} KB)";
                            lblFile.ForeColor = Color.Green;
                            btnTaiFile.Enabled = true;
                        }
                        else
                        {
                            btnTaiFile.Enabled = false;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi tải dữ liệu: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnChonFile_Click(object sender, EventArgs e)
        {
            using (var openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "All files (*.*)|*.*|PDF files (*.pdf)|*.pdf|Word files (*.doc;*.docx)|*.doc;*.docx|Excel files (*.xls;*.xlsx)|*.xls;*.xlsx";
                openFileDialog.FilterIndex = 1;

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        fileData = File.ReadAllBytes(openFileDialog.FileName);
                        lblFile.Text = $"Đã chọn: {Path.GetFileName(openFileDialog.FileName)} ({fileData.Length / 1024} KB)";
                        lblFile.ForeColor = Color.Green;
                        btnTaiFile.Enabled = true;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Lỗi khi đọc file: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void BtnTaiFile_Click(object sender, EventArgs e)
        {
            if (fileData == null || fileData.Length == 0)
            {
                MessageBox.Show("Không có file để tải!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            using (var saveFileDialog = new SaveFileDialog())
            {
                saveFileDialog.Filter = "All files (*.*)|*.*";
                saveFileDialog.FileName = $"SanPhamII_{sanPhamId}";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        File.WriteAllBytes(saveFileDialog.FileName, fileData);
                        MessageBox.Show("Tải file thành công!", "Thông báo", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Lỗi khi tải file: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void ClearAllErrors()
        {
            lblErrorTenBaoCao.Visible = false;
            lblErrorLoaiBaoCao.Visible = false;
        }

        private void ShowError(Label errorLabel, string message)
        {
            errorLabel.Text = message;
            errorLabel.Visible = true;
        }

        private void HideError(Label errorLabel)
        {
            errorLabel.Visible = false;
        }

        private bool ValidateForm()
        {
            ClearAllErrors();
            bool isValid = true;
            Control? firstErrorControl = null;

            // Validate Tên báo cáo (bắt buộc)
            if (string.IsNullOrWhiteSpace(txtTenBaoCao.Text))
            {
                ShowError(lblErrorTenBaoCao, "Vui lòng nhập tên sản phẩm!");
                isValid = false;
                firstErrorControl ??= txtTenBaoCao;
            }

            // Validate Loại báo cáo (bắt buộc)
            if (cmbLoaiBaoCao.SelectedIndex < 0)
            {
                ShowError(lblErrorLoaiBaoCao, "Vui lòng chọn loại sản phẩm!");
                isValid = false;
                firstErrorControl ??= cmbLoaiBaoCao;
            }

            // Focus vào control đầu tiên có lỗi
            firstErrorControl?.Focus();

            return isValid;
        }

        private async void BtnLuu_Click(object sender, EventArgs e)
        {
            if (!ValidateForm())
            {
                return;
            }

            try
            {
                using (var context = new DAContext())
                {
                    ChiTietSanPham_DangII sanPham;

                    if (sanPhamId.HasValue)
                    {
                        // Sửa sản phẩm
                        sanPham = await context.ChiTietSanPham_DangII.FirstOrDefaultAsync(sp => sp.MaSanPham_II == sanPhamId.Value);
                        if (sanPham == null)
                        {
                            MessageBox.Show("Không tìm thấy sản phẩm!", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }
                    }
                    else
                    {
                        // Thêm mới sản phẩm
                        sanPham = new ChiTietSanPham_DangII { MaDeTai = deTaiId };
                        context.ChiTietSanPham_DangII.Add(sanPham);
                    }

                    // Set các thuộc tính theo model mới
                    sanPham.TenSanPham_II = txtTenBaoCao.Text.Trim();

                    // Parse enum từ combobox
                    if (cmbLoaiBaoCao.SelectedItem != null)
                    {
                        string selectedValue = cmbLoaiBaoCao.SelectedItem.ToString()!;
                        if (Enum.TryParse<LoaiSanPham_II>(selectedValue, out var loaiSanPham))
                        {
                            sanPham.LoaiSanPham_II = loaiSanPham;
                        }
                    }

                    // Save file if selected
                    if (fileData != null && fileData.Length > 0)
                    {
                        sanPham.file_SanPham_II = fileData;
                    }

                    await context.SaveChangesAsync();

                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Lỗi khi lưu: {ex.Message}", "Lỗi", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnHuy_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
